//! Raydium 账号数据 Anchor 解析器实现
//!
//! 使用简单直接的方法，避免复杂的宏系统

use async_trait::async_trait;
use anchor_lang::prelude::*;
use solana_sdk::pubkey::Pubkey;
use shared::Result;
use std::collections::HashMap;

use crate::accounts::traits::{
    AccountParser, AccountType, ParsedAccount, AccountData
};
use crate::anchor_types::raydium::{
    RAYDIUM_CLMM_PROGRAM_ID, PoolState, TickArrayState, TickArrayBitmapExtension,
    ObservationState, PersonalPositionState, ProtocolPositionState
};

/// Raydium Anchor 账号解析器
pub struct RaydiumAnchorAccountParser {
    name: String,
    discriminator_map: HashMap<[u8; 8], AccountType>,
}

impl RaydiumAnchorAccountParser {
    /// 创建新的 Raydium Anchor 账号解析器
    pub fn new() -> Self {
        let mut discriminator_map = HashMap::new();

        // 根据 IDL 中的 discriminator 值进行映射
        discriminator_map.insert([247, 237, 227, 245, 215, 195, 222, 70], AccountType::RaydiumPoolState);
        discriminator_map.insert([192, 155, 85, 205, 49, 249, 129, 42], AccountType::RaydiumTickArrayState);
        discriminator_map.insert([60, 150, 36, 219, 97, 128, 139, 153], AccountType::RaydiumTickArrayBitmapExtension);
        discriminator_map.insert([122, 174, 197, 53, 129, 9, 165, 132], AccountType::RaydiumObservationState);
        discriminator_map.insert([70, 111, 150, 126, 230, 15, 25, 117], AccountType::RaydiumPersonalPosition);
        discriminator_map.insert([100, 226, 145, 99, 146, 218, 160, 106], AccountType::RaydiumProtocolPosition);

        Self {
            name: "RaydiumAnchorAccountParser".to_string(),
            discriminator_map,
        }
    }

    /// 使用 discriminator 识别账号类型
    fn identify_account_type_by_discriminator(&self, data: &[u8]) -> Option<AccountType> {
        if data.len() < 8 {
            return None;
        }

        let discriminator: [u8; 8] = data[0..8].try_into().ok()?;
        println!("Raydium Discriminator: {:?}", discriminator);
        self.discriminator_map.get(&discriminator).cloned()
    }

    /// 解析 PoolState 账号
    fn parse_pool_state(&self, address: Pubkey, data: &[u8]) -> Result<Box<dyn AccountData>> {
        let pool_state = PoolState::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize PoolState: {}", e)))?;

        Ok(Box::new(RaydiumPoolStateAdapter {
            address,
            inner: pool_state,
        }))
    }

    /// 解析 TickArrayState 账号
    fn parse_tick_array_state(&self, address: Pubkey, data: &[u8]) -> Result<Box<dyn AccountData>> {
        let tick_array_state = TickArrayState::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize TickArrayState: {}", e)))?;

        Ok(Box::new(RaydiumTickArrayStateAdapter {
            address,
            inner: tick_array_state,
        }))
    }

    /// 解析 TickArrayBitmapExtension 账号
    fn parse_tick_array_bitmap_extension(&self, address: Pubkey, data: &[u8]) -> Result<Box<dyn AccountData>> {
        let bitmap_extension = TickArrayBitmapExtension::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize TickArrayBitmapExtension: {}", e)))?;

        Ok(Box::new(RaydiumTickArrayBitmapExtensionAdapter {
            address,
            inner: bitmap_extension,
        }))
    }

    /// 解析 ObservationState 账号
    fn parse_observation_state(&self, address: Pubkey, data: &[u8]) -> Result<Box<dyn AccountData>> {
        let observation_state = ObservationState::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize ObservationState: {}", e)))?;

        Ok(Box::new(RaydiumObservationStateAdapter {
            address,
            inner: observation_state,
        }))
    }

    /// 解析 PersonalPositionState 账号
    fn parse_personal_position_state(&self, address: Pubkey, data: &[u8]) -> Result<Box<dyn AccountData>> {
        let personal_position = PersonalPositionState::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize PersonalPositionState: {}", e)))?;

        Ok(Box::new(RaydiumPersonalPositionAdapter {
            address,
            inner: personal_position,
        }))
    }

    /// 解析 ProtocolPositionState 账号
    fn parse_protocol_position_state(&self, address: Pubkey, data: &[u8]) -> Result<Box<dyn AccountData>> {
        let protocol_position = ProtocolPositionState::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize ProtocolPositionState: {}", e)))?;

        Ok(Box::new(RaydiumProtocolPositionAdapter {
            address,
            inner: protocol_position,
        }))
    }
}

impl Default for RaydiumAnchorAccountParser {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl AccountParser for RaydiumAnchorAccountParser {
    fn name(&self) -> &str {
        &self.name
    }

    fn supported_program_ids(&self) -> Vec<Pubkey> {
        vec![RAYDIUM_CLMM_PROGRAM_ID]
    }

    fn supported_account_types(&self) -> Vec<AccountType> {
        vec![
            AccountType::RaydiumPoolState,
            AccountType::RaydiumTickArrayState,
            AccountType::RaydiumTickArrayBitmapExtension,
            AccountType::RaydiumObservationState,
            AccountType::RaydiumPersonalPosition,
            AccountType::RaydiumProtocolPosition,
        ]
    }

    fn can_parse(&self, program_id: &Pubkey, account_data: &[u8]) -> bool {
        *program_id == RAYDIUM_CLMM_PROGRAM_ID &&
        self.identify_account_type_by_discriminator(account_data).is_some()
    }

    fn identify_account_type(&self, program_id: &Pubkey, account_data: &[u8]) -> Option<AccountType> {
        if *program_id != RAYDIUM_CLMM_PROGRAM_ID {
            return None;
        }

        self.identify_account_type_by_discriminator(account_data)
    }

    async fn parse_account(
        &self,
        address: Pubkey,
        program_id: Pubkey,
        account_data: &[u8],
    ) -> Result<ParsedAccount> {
        if program_id != RAYDIUM_CLMM_PROGRAM_ID {
            return Err(shared::EchoesError::Parse(
                format!("Unsupported program ID: {}", program_id)
            ));
        }

        let account_type = self.identify_account_type_by_discriminator(account_data)
            .ok_or_else(|| shared::EchoesError::Parse(
                format!("Cannot identify account type for data length: {}", account_data.len())
            ))?;

        let parsed_data = match account_type {
            AccountType::RaydiumPoolState => {
                self.parse_pool_state(address, account_data)?
            }
            AccountType::RaydiumTickArrayState => {
                self.parse_tick_array_state(address, account_data)?
            }
            AccountType::RaydiumTickArrayBitmapExtension => {
                self.parse_tick_array_bitmap_extension(address, account_data)?
            }
            AccountType::RaydiumObservationState => {
                self.parse_observation_state(address, account_data)?
            }
            AccountType::RaydiumPersonalPosition => {
                self.parse_personal_position_state(address, account_data)?
            }
            AccountType::RaydiumProtocolPosition => {
                self.parse_protocol_position_state(address, account_data)?
            }
            _ => {
                return Err(shared::EchoesError::Parse(
                    format!("Unsupported account type: {:?}", account_type)
                ));
            }
        };

        let parsed_account = ParsedAccount::new(
            address,
            program_id,
            account_type,
            parsed_data,
            account_data.len(),
        );

        // 验证解析结果
        self.validate_parsed_data(&parsed_account)?;

        Ok(parsed_account)
    }
}

// 适配器结构体，将 Anchor 类型适配到现有的 AccountData trait
/// PoolState 适配器
#[derive(Debug)]
pub struct RaydiumPoolStateAdapter {
    pub address: Pubkey,
    pub inner: PoolState,
}

impl AccountData for RaydiumPoolStateAdapter {
    fn account_type(&self) -> AccountType {
        AccountType::RaydiumPoolState
    }

    fn address(&self) -> Pubkey {
        self.address
    }

    fn program_id(&self) -> Pubkey {
        RAYDIUM_CLMM_PROGRAM_ID
    }

    fn data_size(&self) -> usize {
        // 注意：实际的 PoolState 大小可能因版本而异
        // 基于实际观察，数据大小约为 1410-1565 字节
        1565 // 基于实际计算的大小（包含 discriminator）
    }

    fn to_json(&self) -> serde_json::Value {
        serde_json::json!({
            "address": self.address.to_string(),
            "bump": self.inner.bump,
            "amm_config": self.inner.amm_config.to_string(),
            "owner": self.inner.owner.to_string(),
            "token_mint_0": self.inner.token_mint_0.to_string(),
            "token_mint_1": self.inner.token_mint_1.to_string(),
            "token_vault_0": self.inner.token_vault_0.to_string(),
            "token_vault_1": self.inner.token_vault_1.to_string(),
            "observation_key": self.inner.observation_key.to_string(),
            "mint_decimals_0": self.inner.mint_decimals_0,
            "mint_decimals_1": self.inner.mint_decimals_1,
            "tick_spacing": self.inner.tick_spacing,
            "liquidity": self.inner.liquidity.to_string(),
            "sqrt_price_x64": self.inner.sqrt_price_x64.to_string(),
            "tick_current": self.inner.tick_current,
            "status": self.inner.status,
        })
    }

    fn creator(&self) -> Option<Pubkey> {
        Some(self.inner.owner)
    }

    fn status(&self) -> Option<u8> {
        Some(self.inner.status)
    }

    fn associated_mints(&self) -> Vec<Pubkey> {
        vec![self.inner.token_mint_0, self.inner.token_mint_1]
    }
}

/// TickArrayState 适配器
#[derive(Debug)]
pub struct RaydiumTickArrayStateAdapter {
    pub address: Pubkey,
    pub inner: TickArrayState,
}

impl AccountData for RaydiumTickArrayStateAdapter {
    fn account_type(&self) -> AccountType {
        AccountType::RaydiumTickArrayState
    }

    fn address(&self) -> Pubkey {
        self.address
    }

    fn program_id(&self) -> Pubkey {
        RAYDIUM_CLMM_PROGRAM_ID
    }

    fn data_size(&self) -> usize {
        32 + 4 + 60 * 136 + 1 + 8 + 107 // 基础结构 + 60个tick * 每个tick大小
    }

    fn to_json(&self) -> serde_json::Value {
        serde_json::json!({
            "address": self.address.to_string(),
            "pool_id": self.inner.pool_id.to_string(),
            "start_tick_index": self.inner.start_tick_index,
            "initialized_tick_count": self.inner.initialized_tick_count,
            "recent_epoch": self.inner.recent_epoch,
        })
    }
}

/// TickArrayBitmapExtension 适配器
#[derive(Debug)]
pub struct RaydiumTickArrayBitmapExtensionAdapter {
    pub address: Pubkey,
    pub inner: TickArrayBitmapExtension,
}

impl AccountData for RaydiumTickArrayBitmapExtensionAdapter {
    fn account_type(&self) -> AccountType {
        AccountType::RaydiumTickArrayBitmapExtension
    }

    fn address(&self) -> Pubkey {
        self.address
    }

    fn program_id(&self) -> Pubkey {
        RAYDIUM_CLMM_PROGRAM_ID
    }

    fn data_size(&self) -> usize {
        32 + 32 + 1024 * 8 + 1024 * 8 // 地址 + 池ID + 两个位图
    }

    fn to_json(&self) -> serde_json::Value {
        serde_json::json!({
            "address": self.address.to_string(),
            "pool_id": self.inner.pool_id.to_string(),
        })
    }
}

/// ObservationState 适配器
#[derive(Debug)]
pub struct RaydiumObservationStateAdapter {
    pub address: Pubkey,
    pub inner: ObservationState,
}

impl AccountData for RaydiumObservationStateAdapter {
    fn account_type(&self) -> AccountType {
        AccountType::RaydiumObservationState
    }

    fn address(&self) -> Pubkey {
        self.address
    }

    fn program_id(&self) -> Pubkey {
        RAYDIUM_CLMM_PROGRAM_ID
    }

    fn data_size(&self) -> usize {
        1 + 8 + 2 + 32 + 100 * 44 + 32 // 基础结构 + 观察数据数组
    }

    fn to_json(&self) -> serde_json::Value {
        serde_json::json!({
            "address": self.address.to_string(),
            "initialized": self.inner.initialized,
            "recent_epoch": self.inner.recent_epoch,
            "observation_index": self.inner.observation_index,
            "pool_id": self.inner.pool_id.to_string(),
        })
    }
}

/// PersonalPositionState 适配器
#[derive(Debug)]
pub struct RaydiumPersonalPositionAdapter {
    pub address: Pubkey,
    pub inner: PersonalPositionState,
}

impl AccountData for RaydiumPersonalPositionAdapter {
    fn account_type(&self) -> AccountType {
        AccountType::RaydiumPersonalPosition
    }

    fn address(&self) -> Pubkey {
        self.address
    }

    fn program_id(&self) -> Pubkey {
        RAYDIUM_CLMM_PROGRAM_ID
    }

    fn data_size(&self) -> usize {
        1 + 32 + 32 + 4 + 4 + 16 + 16 + 16 + 8 + 8 + 48 + 24 // 固定大小的结构
    }

    fn to_json(&self) -> serde_json::Value {
        serde_json::json!({
            "address": self.address.to_string(),
            "bump": self.inner.bump,
            "nft_mint": self.inner.nft_mint.to_string(),
            "pool_id": self.inner.pool_id.to_string(),
            "tick_lower_index": self.inner.tick_lower_index,
            "tick_upper_index": self.inner.tick_upper_index,
            "liquidity": self.inner.liquidity.to_string(),
        })
    }
}

/// ProtocolPositionState 适配器
#[derive(Debug)]
pub struct RaydiumProtocolPositionAdapter {
    pub address: Pubkey,
    pub inner: ProtocolPositionState,
}

impl AccountData for RaydiumProtocolPositionAdapter {
    fn account_type(&self) -> AccountType {
        AccountType::RaydiumProtocolPosition
    }

    fn address(&self) -> Pubkey {
        self.address
    }

    fn program_id(&self) -> Pubkey {
        RAYDIUM_CLMM_PROGRAM_ID
    }

    fn data_size(&self) -> usize {
        1 + 32 + 4 + 4 + 16 + 16 + 16 + 8 + 8 + 48 + 24 // 固定大小的结构
    }

    fn to_json(&self) -> serde_json::Value {
        serde_json::json!({
            "address": self.address.to_string(),
            "bump": self.inner.bump,
            "pool_id": self.inner.pool_id.to_string(),
            "tick_lower_index": self.inner.tick_lower_index,
            "tick_upper_index": self.inner.tick_upper_index,
            "liquidity": self.inner.liquidity.to_string(),
        })
    }
}

